def is_even(number):
    """
    Check if a number is even.
    
    Args:
        number (int): The number to check
        
    Returns:
        bool: True if the number is even, False if odd
    """
    return number % 2 == 0


def main():
    """
    Main function to demonstrate the even number checker.
    """
    # Test with some example numbers
    test_numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 0, -2, -3]
    
    print("Even Number Checker")
    print("-" * 20)
    
    for num in test_numbers:
        if is_even(num):
            print(f"{num} is even")
        else:
            print(f"{num} is odd")
    
    print("\n" + "-" * 20)
    
    # Interactive input
    try:
        user_input = input("Enter a number to check if it's even: ")
        number = int(user_input)
        
        if is_even(number):
            print(f"{number} is even!")
        else:
            print(f"{number} is odd!")
            
    except ValueError:
        print("Please enter a valid integer.")


if __name__ == "__main__":
    main()
